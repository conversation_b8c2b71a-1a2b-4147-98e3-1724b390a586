name: E2E Tests

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  main:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Copy test environment variables
        run: cp example.env .env

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Cache dependencies
        id: cache
        uses: actions/cache@v4
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-${{ hashFiles('**/yarn.lock') }}

      - name: Install dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: yarn install --immutable

      - name: Install Playwright Browsers
        run: npx playwright install chromium --with-deps

      - name: Build apps
        run: yarn turbo build --concurrency=5

      - name: Run dev server
        run: bash e2e/support/github/run-e2e-docker-env.sh

      - name: Wait for OpenMRS instance to start
        run: while [[ "$(curl -s -o /dev/null -w ''%{http_code}'' http://localhost:8080/openmrs/login.htm)" != "200" ]]; do sleep 10; done

      - name: Run E2E tests
        run: yarn test-e2e

      - name: 🛑 Stop dev server
        if: '!cancelled()'
        run: docker stop $(docker ps -a -q)

      - name: Upload report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
