name: OpenMRS CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  release:
    types:
      - created

env:
  ESM_NAME: "@openmrs/esm-template-app"
  JS_NAME: "openmrs-esm-template-app.js"

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: yarn install --immutable
      - run: yarn verify
      - run: yarn build
      - name: Upload Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: |
            dist

  pre_release:
    runs-on: ubuntu-latest

    needs: build

    if: ${{ github.event_name == 'push' }}

    steps:
      - run: echo "Uncomment the lines below and delete this one."
      # - uses: actions/checkout@v4
      # - name: Download Artifacts
      #   uses: actions/download-artifact@v4
      # - name: Use Node.js
      #   uses: actions/setup-node@v4
      #   with:
      #     node-version: 20
      #     registry-url: "https://registry.npmjs.org"
      # - run: yarn install --immutable
      # - run: yarn version "$(node -e "console.log(require('semver').inc(require('./package.json').version, 'patch'))")-pre.${{ github.run_number }}"
      # - run: yarn build
      # - run: git config user.email "<EMAIL>" && git config user.name "OpenMRS CI"
      # - run: git add . && git commit -m "Prerelease version" --no-verify
      # - run: yarn config set npmAuthToken "${NODE_AUTH_TOKEN}" && yarn npm publish --access public --tag next
      #   env:
      #     NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
      # - name: Upload Artifacts
      #   uses: actions/upload-artifact@v4
      #   with:
      #     name: dist
      #     path: |
      #       dist

  release:
    runs-on: ubuntu-latest

    needs: build

    if: ${{ github.event_name == 'release' }}

    steps:
      - uses: actions/checkout@v4
      - name: Download Artifacts
        uses: actions/download-artifact@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: 'https://registry.npmjs.org'
      - run: yarn install --immutable
      - run: yarn config set npmAuthToken "${NODE_AUTH_TOKEN}" &&  yarn npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}

  deploy:
    runs-on: ubuntu-latest

    needs: pre_release

    if: ${{ github.event_name == 'push' }}

    steps:
      - run: echo "Uncomment the lines below and delete this one."
      # - name: Trigger RefApp Build
      #   uses: fjogeleit/http-request-action@v1
      #   with:
      #     url: https://ci.openmrs.org/rest/api/latest/queue/O3-BP
      #     method: "POST"
      #     customHeaders: '{ "Authorization": "Bearer ${{ secrets.BAMBOO_TOKEN }}" }'
