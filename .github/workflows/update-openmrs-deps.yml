# Workflow to automatically check and update OpenMRS dependencies
# Runs hourly and can be triggered manually

name: 'Check for OpenMRS Dependency Updates'

on:
  workflow_dispatch:
  schedule:
    # Runs every hour at minute 30
    - cron: '30 * * * *'

jobs:
  check-for-updates:
    name: Check for updates to OpenMRS libraries
    runs-on: ubuntu-latest

    if: github.repository_owner == 'openmrs'

    permissions:
      contents: write
      pull-requests: write

    steps:
      # Step 1: Check out repository
      - name: Checkout repository
        uses: actions/checkout@v4

      # Step 2: Setup Node.js environment
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # Step 3: Cache dependencies
      - name: 💾 Cache dependencies
        id: cache
        uses: actions/cache@v4
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-${{ hashFiles('**/yarn.lock') }}

      # Step 4: Install dependencies if cache miss
      - name: 📦 Install dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: yarn install --immutable

      # Step 5: Run dependency update check
      - name: ✅ Check for updates
        run: node ./tools/update-openmrs-deps.mjs

      # Step 6: Create PR with updates if necessary
      - name: ⬆️ Create PR if necessary
        id: cpr
        uses: peter-evans/create-pull-request@v7
        with:
          commit-message: '(chore) Update OpenMRS dependencies'
          title: '(chore) Update OpenMRS dependencies'
          body: |
            # OpenMRS Dependencies Update

            This PR contains updates to OpenMRS dependencies.

            ## Changes
            * Automated dependency updates for OpenMRS packages
            * Generated by the OpenMRS Dependency Update workflow

            ## Verification
            - [ ] All dependencies are valid versions
            - [ ] No breaking changes introduced

            > This PR was automatically generated and will be automatically merged if checks pass.
          branch: 'chore/update-openmrs-deps'
          author: 'OpenMRS Bot <<EMAIL>>'
          token: ${{ secrets.OMRS_BOT_GH_TOKEN }}
          labels: |
            dependencies
            automated-pr
          delete-branch: true    # Clean up branch after merge

      # Step 7: Auto-approve the PR if created or updated
      - name: ✅ Auto approve PR
        if: steps.cpr.outputs.pull-request-operation == 'created' || steps.cpr.outputs.pull-request-operation == 'updated'
        run: gh pr review --approve "${{ steps.cpr.outputs.pull-request-number }}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # Step 8: Auto-merge the PR if created or updated
      - name: 🔀 Auto merge PR
        if: steps.cpr.outputs.pull-request-operation == 'created' || steps.cpr.outputs.pull-request-operation == 'updated'
        run: gh pr merge --auto --squash "${{ steps.cpr.outputs.pull-request-number }}"
        env:
          GH_TOKEN: ${{ secrets.OMRS_BOT_GH_TOKEN }}
