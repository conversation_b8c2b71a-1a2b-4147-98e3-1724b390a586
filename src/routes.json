{"$schema": "https://json.openmrs.org/routes.schema.json", "backendDependencies": {"fhir2": ">=1.2", "webservices.rest": "^2.24.0"}, "extensions": [{"name": "Red box", "component": "redBox", "slot": "Boxes"}, {"name": "Blue box", "component": "blueBox", "slot": "Boxes"}, {"name": "Brand box", "component": "blueBox", "slot": "Boxes"}, {"name": "Refer <PERSON><PERSON>", "component": "referPatientButton", "slot": "patient-actions-slot"}, {"name": "New Nav <PERSON>ton", "component": "newNavButton", "slot": "patient-chart-dashboard-slot", "order": 999}], "pages": [{"component": "root", "route": "root"}, {"component": "referralFormWorkspace", "route": "referral-form"}]}