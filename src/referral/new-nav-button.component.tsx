import React from 'react';
import { useTranslation } from 'react-i18next';
import { SideNavLink } from '@carbon/react';
import { DocumentImport } from '@carbon/react/icons';

export default function NewNavButton() {
  const { t } = useTranslation();

  return (
    <SideNavLink
      renderIcon={(props) => <DocumentImport {...props} />}
      href={window.getOpenmrsSpaBase() + 'referral-history'}
    >
      {t('referralHistory', 'Referral History')}
    </SideNavLink>
  );
}
