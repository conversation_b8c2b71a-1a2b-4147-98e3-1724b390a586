import React from 'react';
import { useTranslation } from 'react-i18next';
import { SideNavLink } from '@carbon/react';
import { Settings } from '@carbon/react/icons';

export default function NewNavButton() {
  const { t } = useTranslation();

  return (
    <SideNavLink
      renderIcon={(props) => <Settings {...props} />}
      href={window.getOpenmrsSpaBase() + 'new-feature'}
    >
      {t('newFeature', 'New Feature')}
    </SideNavLink>
  );
}
