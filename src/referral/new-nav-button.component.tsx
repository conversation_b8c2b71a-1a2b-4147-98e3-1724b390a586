import React from 'react';
import { useTranslation } from 'react-i18next';
import { ConfigurableLink } from '@openmrs/esm-framework';
import { DocumentImport } from '@carbon/react/icons';

export default function ReferralHistoryDashboardLink() {
  const { t } = useTranslation();

  return (
    <ConfigurableLink
      to="${openmrsSpaBase}/patient/${patientUuid}/chart/referral-history"
    >
      <DocumentImport size={16} />
      {t('referralHistory', 'Referral History')}
    </ConfigurableLink>
  );
}
