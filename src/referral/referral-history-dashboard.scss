@use '@carbon/layout';
@use '@carbon/type';

.container {
  padding: layout.$spacing-05;
  max-width: 100%;
}

.header {
  margin-bottom: layout.$spacing-06;
  
  .title {
    @include type.type-style('heading-compact-02');
    display: flex;
    align-items: center;
    gap: layout.$spacing-03;
    margin-bottom: layout.$spacing-03;
    color: #161616;
  }
  
  .subtitle {
    @include type.type-style('body-short-01');
    color: #525252;
    margin: 0;
  }
}

.content {
  padding: layout.$spacing-05;
}

.status {
  padding: layout.$spacing-02 layout.$spacing-03;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  
  &.completed {
    background-color: #d4edda;
    color: #155724;
  }
  
  &.pending {
    background-color: #fff3cd;
    color: #856404;
  }
  
  &.cancelled {
    background-color: #f8d7da;
    color: #721c24;
  }
}

.urgency {
  padding: layout.$spacing-02 layout.$spacing-03;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  
  &.high {
    background-color: #ffebee;
    color: #c62828;
  }
  
  &.medium {
    background-color: #fff8e1;
    color: #f57c00;
  }
  
  &.low {
    background-color: #e8f5e8;
    color: #2e7d32;
  }
}
