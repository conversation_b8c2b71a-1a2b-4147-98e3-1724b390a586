import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Modal,
  TextInput,
  TextArea,
  Select,
  SelectItem,
  Stack,
} from '@carbon/react';
import styles from './referral-modal.scss';
import ReferralLetterModal from './referral-letter-modal.component';

interface ReferralModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ReferralModal: React.FC<ReferralModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const [isLetterModalOpen, setIsLetterModalOpen] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    hospitalName: '',
    hospitalAddress: '',
    urgencyLevel: '',
    reasonForReferral: '',
    conditionSummary: '',
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleProceed = () => {
    // Hide details modal and show letter modal
    setShowDetailsModal(false);
    setIsLetterModalOpen(true);
  };

  const handleLetterModalClose = () => {
    setIsLetterModalOpen(false);
    setShowDetailsModal(true);
    onClose();
  };

  const handleDetailsModalClose = () => {
    setShowDetailsModal(true);
    setIsLetterModalOpen(false);
    onClose();
  };

  return (
    <>
      <Modal
        open={isOpen && showDetailsModal}
        onRequestClose={handleDetailsModalClose}
        modalHeading="Referral Details"
        primaryButtonText="Proceed"
        secondaryButtonText="Close"
        onRequestSubmit={handleProceed}
        className={styles.referralModal}
      >
        <div className={styles.modalContent}>
          <p className={styles.description}>
            Provide the details below before you can refer this patient to another doctor.
          </p>

          <div className={styles.section}>
            <h4 className={styles.sectionTitle}>Recipient Information</h4>
            
            <Stack gap={5}>
              <TextInput
                id="name"
                labelText="Name"
                placeholder="Type here..."
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
              />

              <TextInput
                id="hospitalName"
                labelText="Hospital Name"
                placeholder="Type here..."
                value={formData.hospitalName}
                onChange={(e) => handleInputChange('hospitalName', e.target.value)}
              />

              <TextInput
                id="hospitalAddress"
                labelText="Hospital Address"
                placeholder="Type here..."
                value={formData.hospitalAddress}
                onChange={(e) => handleInputChange('hospitalAddress', e.target.value)}
              />
            </Stack>
          </div>

          <div className={styles.section}>
            <h4 className={styles.sectionTitle}>Other Information</h4>
            
            <Stack gap={5}>
              <Select
                id="urgencyLevel"
                labelText="Urgency Level"
                value={formData.urgencyLevel}
                onChange={(e) => handleInputChange('urgencyLevel', e.target.value)}
              >
                <SelectItem value="" text="Select" />
                <SelectItem value="low" text="Low" />
                <SelectItem value="medium" text="Medium" />
                <SelectItem value="high" text="High" />
                <SelectItem value="urgent" text="Urgent" />
              </Select>

              <TextArea
                id="reasonForReferral"
                labelText="Reason for referral"
                placeholder="Type here..."
                value={formData.reasonForReferral}
                onChange={(e) => handleInputChange('reasonForReferral', e.target.value)}
                rows={3}
              />

              <TextArea
                id="conditionSummary"
                labelText="Condition/Symptoms Summary"
                placeholder="Type here..."
                value={formData.conditionSummary}
                onChange={(e) => handleInputChange('conditionSummary', e.target.value)}
                rows={3}
              />
            </Stack>
          </div>
        </div>
      </Modal>
      
      <ReferralLetterModal
        isOpen={isLetterModalOpen}
        onClose={handleLetterModalClose}
        referralData={formData}
      />
    </>
  );
};

export default ReferralModal; 