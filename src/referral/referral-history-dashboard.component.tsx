import React from 'react';
import { useTranslation } from 'react-i18next';
import { 
  DataTable,
  Table,
  TableHead,
  TableRow,
  TableHeader,
  TableBody,
  TableCell,
  TableContainer,
  Tile,
  SkeletonText,
  EmptyState
} from '@carbon/react';
import { DocumentImport } from '@carbon/react/icons';
import styles from './referral-history-dashboard.scss';

interface ReferralRecord {
  id: string;
  date: string;
  referredTo: string;
  reason: string;
  status: string;
  urgency: string;
}

// Mock data - replace with actual data fetching
const mockReferralData: ReferralRecord[] = [
  {
    id: '1',
    date: '2024-01-15',
    referredTo: 'Dr. <PERSON>',
    reason: 'Chest pain evaluation',
    status: 'Completed',
    urgency: 'High'
  },
  {
    id: '2',
    date: '2024-01-10',
    referredTo: 'Dr. <PERSON>',
    reason: 'Knee pain assessment',
    status: 'Pending',
    urgency: 'Medium'
  },
  {
    id: '3',
    date: '2024-01-05',
    referredTo: '<PERSON>. <PERSON> - Dermatology',
    reason: 'Skin condition review',
    status: 'Completed',
    urgency: 'Low'
  }
];

const headers = [
  { key: 'date', header: 'Date' },
  { key: 'referredTo', header: 'Referred To' },
  { key: 'reason', header: 'Reason' },
  { key: 'urgency', header: 'Urgency' },
  { key: 'status', header: 'Status' }
];

export default function ReferralHistoryDashboard() {
  const { t } = useTranslation();
  const [isLoading] = React.useState(false);

  if (isLoading) {
    return (
      <div className={styles.container}>
        <SkeletonText />
      </div>
    );
  }

  if (mockReferralData.length === 0) {
    return (
      <div className={styles.container}>
        <EmptyState
          headerTitle={t('noReferrals', 'No referrals found')}
          headerSubtitle={t('noReferralsSubtitle', 'This patient has no referral history.')}
        />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h4 className={styles.title}>
          <DocumentImport size={20} />
          {t('referralHistory', 'Referral History')}
        </h4>
        <p className={styles.subtitle}>
          {t('referralHistorySubtitle', 'View all referrals made for this patient')}
        </p>
      </div>

      <Tile className={styles.content}>
        <DataTable rows={mockReferralData} headers={headers}>
          {({ rows, headers, getTableProps, getHeaderProps, getRowProps }) => (
            <TableContainer>
              <Table {...getTableProps()}>
                <TableHead>
                  <TableRow>
                    {headers.map((header) => (
                      <TableHeader {...getHeaderProps({ header })}>
                        {header.header}
                      </TableHeader>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {rows.map((row) => (
                    <TableRow {...getRowProps({ row })}>
                      {row.cells.map((cell) => (
                        <TableCell key={cell.id}>
                          {cell.id.includes('status') ? (
                            <span className={`${styles.status} ${styles[cell.value.toLowerCase()]}`}>
                              {cell.value}
                            </span>
                          ) : cell.id.includes('urgency') ? (
                            <span className={`${styles.urgency} ${styles[cell.value.toLowerCase()]}`}>
                              {cell.value}
                            </span>
                          ) : (
                            cell.value
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </DataTable>
      </Tile>
    </div>
  );
}
