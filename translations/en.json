{"casualGreeting": "hey", "configSystem": "Configuration system", "configSystemExplainer": "The greeting shown below is driven by the configuration system. To change the configuration properties, click the spanner icon in the navbar to pull up the Implementer Tools panel. Then, type <2>template</2> into the <4>Search configuration</4> input. This should filter the configuration properties to show only those that are relevant to this module. You can change the values of these properties and click <6>Save</6> to see the changes reflected in the UI", "connect": "Connect", "connectExplainer": "Get in touch with the community", "dataFetching": "Data fetching", "designDocs": "Design docs", "designDocsExplainer": "Read the O3 design documentation", "explainer": "The following examples demonstrate some key features of the O3 framework", "extensionExplainer": "Here are some colored boxes. Because they are attached as extensions within a slot, an admin can change what boxes are shown using configuration. These boxes happen to be defined in this module, but they could attach to this slot even if they were in a different module", "extensionSystem": "Extension system", "formalGreeting": "hello", "frontendDocs": "Frontend docs", "getPatient": "Get a patient named", "getStarted": "Get started", "getStartedExplainer": "Create a frontend module from this template", "learnExplainer": "Learn how to use the O3 framework", "loading": "Loading", "patientGetterExplainer": "Try clicking the button below to fetch a patient from the backend", "resources": "Resources", "usefulLinks": "Below are some links to useful resources", "welcomeText": "Welcome to the O3 Template app", "referPatient": "Refer Patient", "referralHistory": "Referral History"}